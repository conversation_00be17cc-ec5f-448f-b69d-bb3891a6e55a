import { component$ } from '@qwik.dev/core';
import { Image } from 'qwik-image';
import { $localize } from '~/utils/localize';

export default component$(() => {
	return (
		<div class="bg-background">
			{/* Hero Section */}
			<div class="relative py-16 bg-gradient-to-b from-black to-background">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center">
						<h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">
							{$localize`About SugarJays`}
						</h1>
						<p class="mt-4 max-w-3xl mx-auto text-xl text-text">
							{$localize`Custom collaborative apparel for individuals and businesses who want to make a statement.`}
						</p>
					</div>
				</div>
			</div>

			{/* Our Story Section */}
			<div class="py-16 overflow-hidden">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="lg:grid lg:grid-cols-2 lg:gap-8">
						<div>
							<h2 class="text-3xl font-extrabold tracking-tight text-accent1 sm:text-4xl">
								{$localize`Our Story`}
							</h2>
							<div class="mt-3 text-lg text-text space-y-4">
								<p>
									{$localize`Our journey began with a simple mission: to create a brand that combines comfort, quality, and fashion in a way that speaks to everyone.`}
								</p>
								<p>
									{$localize`Whether you're looking for the perfect casual outfit, something to stand out on a special occasion, or a wardrobe staple that complements your unique style, we've got you covered.`}
								</p>
								<p>
									{$localize`From day one, we've been dedicated to offering a wide range of clothing for both men and women, blending modern streetwear influences with classic silhouettes. Every design is carefully crafted, ensuring that each piece not only looks great but feels great too.`}
								</p>
								<p>
									{$localize`With an eye for detail and a commitment to sustainability, we source high-quality fabrics to create apparel that stands the test of time.`}
								</p>
								<p>
									{$localize`At SugarJays Apparel, we believe in the power of self-expression through fashion. Our collections are made for those who want to make a statement, those who value comfort just as much as style, and those who aren't afraid to stand out.`}
								</p>
								<p>
									{$localize`We're here to help you feel confident, bold, and ready to take on the world—one outfit at a time. We're more than just a clothing brand. We're a community of individuals who share a passion for creativity, comfort, and authentic style.`}
								</p>
								<p>
									{$localize`Join us and become part of the SugarJays Apparel family. Together, we can redefine what it means to dress with purpose.`}
								</p>
							</div>
						</div>
						<div class="mt-12 relative lg:mt-0">
							<div class="lg:absolute lg:inset-0">
								<Image
									layout="constrained"
									class="lg:h-full lg:w-full rounded-lg shadow-xl ring-1 ring-primary/20"
									width="600"
									height="600"
									src="/about-story.png"
									alt="SugarJays workshop with designers collaborating"
									placeholder="#1f1f1f"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Values Section */}
			<div class="bg-gradient-to-r from-primary/10 to-secondary/10 py-16">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center">
						<h2 class="text-3xl font-extrabold tracking-tight text-accent1 sm:text-4xl">
							{$localize`Our Values`}
						</h2>
						<p class="mt-4 max-w-3xl mx-auto text-lg text-text">
							{$localize`These core principles guide everything we do at SugarJays.`}
						</p>
					</div>

					<div class="mt-10">
						<div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
							<div class="pt-6">
								<div class="flow-root bg-background rounded-lg px-6 pb-8 shadow-md">
									<div class="-mt-6">
										<div>
											<span class="inline-flex items-center justify-center p-3 bg-accent1 rounded-md shadow-lg">
												<svg
													xmlns="http://www.w3.org/2000/svg"
													class="h-6 w-6 text-white"
													fill="none"
													viewBox="0 0 24 24"
													stroke="currentColor"
												>
													<path
														stroke-linecap="round"
														stroke-linejoin="round"
														stroke-width="2"
														d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4"
													/>
												</svg>
											</span>
										</div>
										<h3 class="mt-8 text-lg font-medium text-accent1 tracking-tight">
											{$localize`Quality Craftsmanship`}
										</h3>
										<p class="mt-5 text-base text-text">
											{$localize`We use premium materials and meticulous production processes to ensure every piece meets our high standards.`}
										</p>
									</div>
								</div>
							</div>

							<div class="pt-6">
								<div class="flow-root bg-background rounded-lg px-6 pb-8 shadow-md">
									<div class="-mt-6">
										<div>
											<span class="inline-flex items-center justify-center p-3 bg-accent1 rounded-md shadow-lg">
												<svg
													xmlns="http://www.w3.org/2000/svg"
													class="h-6 w-6 text-white"
													fill="none"
													viewBox="0 0 24 24"
													stroke="currentColor"
												>
													<path
														stroke-linecap="round"
														stroke-linejoin="round"
														stroke-width="2"
														d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
													/>
												</svg>
											</span>
										</div>
										<h3 class="mt-8 text-lg font-medium text-accent1 tracking-tight">
											{$localize`Sustainability`}
										</h3>
										<p class="mt-5 text-base text-text">
											{$localize`We are committed to eco-friendly practices, from sourcing materials to minimizing waste in our production process.`}
										</p>
									</div>
								</div>
							</div>

							<div class="pt-6">
								<div class="flow-root bg-background rounded-lg px-6 pb-8 shadow-md">
									<div class="-mt-6">
										<div>
											<span class="inline-flex items-center justify-center p-3 bg-accent1 rounded-md shadow-lg">
												<svg
													xmlns="http://www.w3.org/2000/svg"
													class="h-6 w-6 text-white"
													fill="none"
													viewBox="0 0 24 24"
													stroke="currentColor"
												>
													<path
														stroke-linecap="round"
														stroke-linejoin="round"
														stroke-width="2"
														d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
													/>
												</svg>
											</span>
										</div>
										<h3 class="mt-8 text-lg font-medium text-accent1 tracking-tight">
											{$localize`Collaborative Design`}
										</h3>
										<p class="mt-5 text-base text-text">
											{$localize`We believe in working hand-in-hand with our clients to bring their vision to life, ensuring a truly personalized result.`}
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* CTA Section */}
			<div class="bg-gradient-to-r from-primary/20 to-secondary/20 py-16">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center">
						<h2 class="text-3xl font-extrabold tracking-tight text-accent1 sm:text-4xl">
							{$localize`Ready to create something unique?`}
						</h2>
						<p class="mt-4 max-w-3xl mx-auto text-lg text-text">
							{$localize`Let us collaborate on your next custom apparel project.`}
						</p>
						<div class="mt-8 flex justify-center">
							<a
								href="/contact"
								class="px-8 py-3 rounded-md bg-primary text-white font-medium hover:bg-secondary transition-colors"
							>
								{$localize`Get in Touch`}
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
});
