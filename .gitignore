# Server
one-click-deploy

# Build
build
dist
lib
server
functions/**/*.js

# Development
node_modules
.pnpm-store

# Cache
.cache
.mf
.vscode
.rollup.cache
tsconfig.tsbuildinfo

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Yarn
.yarn/*
!.yarn/releases

# Cloudflare
