{"compilerOptions": {"allowJs": true, "target": "ES2017", "module": "ES2020", "lib": ["es2020", "DOM", "WebWorker", "DOM.Iterable"], "jsx": "react-jsx", "jsxImportSource": "@qwik.dev/core", "strict": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "incremental": true, "isolatedModules": true, "outDir": "tmp", "noEmit": true, "types": ["node", "vite/client"], "paths": {"~/*": ["./src/*"]}}, "files": ["./.eslintrc.cjs"], "exclude": ["src/generated/graphql-shop.ts"], "include": ["src", "tailwind.config.js", "codegen-shop.ts"]}