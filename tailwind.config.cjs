const colors = require('tailwindcss/colors');

module.exports = {
	content: ['./src/**/*.{js,ts,jsx,tsx}'],
	theme: {
		extend: {
			colors: {
				primary: {
					DEFAULT: '#FF00FF', // Neon Pink
					...colors.pink, // Keep existing shades if needed, or define specific shades
				},
				secondary: {
					DEFAULT: '#BF00FF', // Neon Purple
					...colors.purple,
				},
				accent1: '#00FFFF', // Neon Cyan
				accent2: '#00FF00', // Neon Green
				// Add more accent colors as needed
				background: '#1A1A1A', // Dark background
				text: '#FFFFFF', // White text
				// You might want to define shades for these as well
			},
			animation: {
				'fade-in': 'fadeIn 0.3s ease-out forwards',
			},
			keyframes: {
				fadeIn: {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' },
				},
			},

		},
		variants: {
			extend: {
				opacity: ['disabled'],
				transform: ['hover', 'focus'],
				scale: ['hover', 'focus'],
			},
		},
	},
	plugins: [require('@tailwindcss/forms')],
};
