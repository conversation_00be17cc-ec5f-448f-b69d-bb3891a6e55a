import { component$ } from '@qwik.dev/core';
import { $localize } from '~/utils/localize';

export default component$(() => {
	const services = [
		{
			name: 'Custom Apparel Design',
			description:
				'From concept to creation, we work with you to design custom apparel that perfectly represents your brand or personal style.',
			icon: (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-10 w-10 text-white"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
					/>
				</svg>
			),
			features: [
				'Collaborative design process',
				'Professional graphic design services',
				'Multiple revision rounds',
				'Digital mockups before production',
			],
		},
		{
			name: 'Screen Printing',
			description:
				'High-quality screen printing services for vibrant, durable designs that stand the test of time.',
			icon: (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-10 w-10 text-white"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"
					/>
				</svg>
			),
			features: [
				'Up to 8 colors per design',
				'Water-based and plastisol inks',
				'Specialty inks available',
				'Bulk order discounts',
			],
		},
		{
			name: 'Embroidery',
			description:
				'Add a touch of class with our precision embroidery services, perfect for corporate apparel and premium products.',
			icon: (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-10 w-10 text-white"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
					/>
				</svg>
			),
			features: [
				'High-quality thread options',
				'Detailed logo reproduction',
				'Multiple placement options',
				'Durable and professional finish',
			],
		},
		{
			name: 'Direct-to-Garment Printing',
			description:
				'Perfect for complex, multi-color designs and small batch orders with photographic quality results.',
			icon: (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-10 w-10 text-white"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
					/>
				</svg>
			),
			features: [
				'Full-color printing capability',
				'Ideal for photographic images',
				'No minimum order requirements',
				'Soft hand feel on garments',
			],
		},
		{
			name: 'Bulk Orders',
			description:
				'Competitive pricing and consistent quality for large orders, perfect for events, teams, and corporate needs.',
			icon: (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-10 w-10 text-white"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
					/>
				</svg>
			),
			features: [
				'Volume discounts',
				'Consistent quality control',
				'Organized packaging options',
				'Expedited production available',
			],
		},
		{
			name: 'Fulfillment Services',
			description:
				'End-to-end solutions including storage, packaging, and shipping directly to your customers.',
			icon: (
				<svg
					xmlns="http://www.w3.org/2000/svg"
					class="h-10 w-10 text-white"
					fill="none"
					viewBox="0 0 24 24"
					stroke="currentColor"
				>
					<path
						stroke-linecap="round"
						stroke-linejoin="round"
						stroke-width="2"
						d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
					/>
				</svg>
			),
			features: [
				'Inventory management',
				'Custom packaging options',
				'Direct-to-customer shipping',
				'Order tracking and reporting',
			],
		},
	];

	const processSteps = [
		{
			step: 1,
			title: 'Consultation',
			description:
				'We start with a detailed discussion about your vision, requirements, and goals for your custom apparel project.',
		},
		{
			step: 2,
			title: 'Design',
			description:
				'Our design team creates concepts based on your input, providing digital mockups for your review and feedback.',
		},
		{
			step: 3,
			title: 'Refinement',
			description:
				'We refine the designs based on your feedback until you are completely satisfied with the final concept.',
		},
		{
			step: 4,
			title: 'Production',
			description:
				'Once approved, we move to production, using premium materials and techniques to create your custom apparel.',
		},
		{
			step: 5,
			title: 'Quality Control',
			description:
				'Every item undergoes rigorous quality checks to ensure it meets our high standards before shipping.',
		},
		{
			step: 6,
			title: 'Delivery',
			description:
				'Your finished products are carefully packaged and delivered to your specified location on time.',
		},
	];

	return (
		<div class="bg-background">
			{/* Hero Section */}
			<div class="relative py-16 bg-gradient-to-b from-black to-background">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center">
						<h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">
							{$localize`Our Services`}
						</h1>
						<p class="mt-4 max-w-3xl mx-auto text-xl text-text">
							{$localize`Comprehensive custom apparel solutions for individuals and businesses.`}
						</p>
					</div>
				</div>
			</div>

			{/* Services Grid */}
			<div class="py-12 bg-background">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center mb-12">
						<h2 class="text-3xl font-extrabold text-accent1 sm:text-4xl">
							{$localize`What We Offer`}
						</h2>
						<p class="mt-4 max-w-3xl mx-auto text-lg text-text">
							{$localize`Comprehensive custom apparel services tailored to your unique needs.`}
						</p>
					</div>

					<div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
						{services.map((service, index) => (
							<div key={service.name}>
								<div class="h-full flex flex-col bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg shadow-lg overflow-hidden">
									<div class="p-6">
										<div class="flex items-center">
											<div class="flex-shrink-0">
												<span class="inline-flex items-center justify-center h-16 w-16 rounded-md bg-accent1 shadow-lg">
													{service.icon}
												</span>
											</div>
											<div class="ml-4">
												<h3 class="text-lg font-medium text-accent1">{service.name}</h3>
											</div>
										</div>
										<div class="mt-4">
											<p class="text-base text-text">{service.description}</p>
										</div>
										<div class="mt-6 flex-grow">
											<ul class="space-y-2">
												{service.features.map((feature) => (
													<li key={feature} class="flex items-start">
														<div class="flex-shrink-0">
															<svg
																class="h-5 w-5 text-accent1"
																xmlns="http://www.w3.org/2000/svg"
																viewBox="0 0 20 20"
																fill="currentColor"
															>
																<path
																	fill-rule="evenodd"
																	d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
																	clip-rule="evenodd"
																/>
															</svg>
														</div>
														<p class="ml-3 text-sm text-text">{feature}</p>
													</li>
												))}
											</ul>
										</div>
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Process Section */}
			<div class="py-16 bg-gradient-to-r from-primary/10 to-secondary/10">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center mb-12">
						<h2 class="text-3xl font-extrabold text-accent1 sm:text-4xl">
							{$localize`Our Process`}
						</h2>
						<p class="mt-4 max-w-3xl mx-auto text-lg text-text">
							{$localize`How we bring your custom apparel vision to life.`}
						</p>
					</div>

					<div class="relative">
						{/* Process timeline line */}
						<div class="absolute left-1/2 transform -translate-x-px h-full w-0.5 bg-gradient-to-b from-primary via-accent1 to-secondary"></div>

						{/* Process steps */}
						<div class="relative">
							{processSteps.map((step, index) => (
								<div key={step.step} class="relative mb-12">
									<div
										class={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}
									>
										<div class={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8'}`}>
											<h3 class="text-xl font-bold text-accent1">{step.title}</h3>
											<p class="mt-2 text-base text-text">{step.description}</p>
										</div>
										<div class="flex-shrink-0 w-12 h-12 rounded-full bg-accent1 flex items-center justify-center z-10 shadow-lg">
											<span class="text-white font-bold">{step.step}</span>
										</div>
										<div class={`w-1/2 ${index % 2 === 0 ? 'pl-8' : 'pr-8 text-right'}`}></div>
									</div>
								</div>
							))}
						</div>
					</div>
				</div>
			</div>

			{/* Testimonials Section */}
			<div class="py-16 bg-background">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center mb-12">
						<h2 class="text-3xl font-extrabold text-accent1 sm:text-4xl">
							{$localize`What Our Clients Say`}
						</h2>
						<p class="mt-4 max-w-3xl mx-auto text-lg text-text">
							{$localize`Do not just take our word for it — hear from our satisfied customers.`}
						</p>
					</div>

					<div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
						<div class="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg shadow-lg p-8">
							<div class="flex items-center mb-6">
								<div class="mr-4 flex-shrink-0">
									<span class="inline-flex items-center justify-center h-12 w-12 rounded-full bg-accent1">
										<span class="text-lg font-medium text-white">BC</span>
									</span>
								</div>
								<div>
									<h4 class="text-lg font-medium text-accent1">Bright Creations</h4>
									<p class="text-sm text-primary">Marketing Agency</p>
								</div>
							</div>
							<blockquote class="text-text italic">
								&quot;SugarJays delivered beyond our expectations for our company retreat. The
								quality of the custom t-shirts was exceptional, and they managed to capture our
								brand identity perfectly. Their team was responsive and accommodating throughout the
								entire process.&quot;
							</blockquote>
						</div>

						<div class="bg-gradient-to-br from-primary/5 to-secondary/5 rounded-lg shadow-lg p-8">
							<div class="flex items-center mb-6">
								<div class="mr-4 flex-shrink-0">
									<span class="inline-flex items-center justify-center h-12 w-12 rounded-full bg-accent1">
										<span class="text-lg font-medium text-white">TF</span>
									</span>
								</div>
								<div>
									<h4 class="text-lg font-medium text-accent1">Tech Forward</h4>
									<p class="text-sm text-primary">Software Company</p>
								</div>
							</div>
							<blockquote class="text-text italic">
								&quot;We have worked with several custom apparel companies in the past, but
								SugarJays stands out for their attention to detail and collaborative approach. They
								took the time to understand our needs and delivered high-quality products that our
								team loves to wear.&quot;
							</blockquote>
						</div>
					</div>
				</div>
			</div>

			{/* CTA Section */}
			<div class="py-16 bg-gradient-to-r from-primary/20 to-secondary/20">
				<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div class="text-center">
						<h2 class="text-3xl font-extrabold text-accent1 sm:text-4xl">
							{$localize`Ready to start your custom apparel project?`}
						</h2>
						<p class="mt-4 max-w-3xl mx-auto text-lg text-text">
							{$localize`Contact us today to discuss your needs and get a free quote.`}
						</p>
						<div class="mt-8 flex justify-center">
							<a
								href="/contact"
								class="px-8 py-3 rounded-md bg-primary text-white font-medium hover:bg-secondary transition-colors"
							>
								{$localize`Get Started`}
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
});
